generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth Models
model user {
  id String @id @unique @default(cuid())

  // Standard fields
  firstName     String?
  lastName      String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  phone         String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  customerId String? @default("")

  role   role?   @relation(fields: [roleId], references: [id])
  roleId String?

  accounts         account[]
  sessions         session[]
  // Optional for WebAuthn support
  authenticator    authenticator[]
  pushSubscription pushSubscription?
  notificationLog  notificationLog[]
  notification     notification[]
  socket           socket[]

  company   company? @relation(fields: [companyId], references: [id])
  companyId String?
}

model company {
  id   String @id @unique @default(cuid())
  name String @unique

  tin      String
  category String?
  email    String?

  address String?
  phone   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user user[]
}

model account {
  id String @id @unique @default(cuid())

  password String?

  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user   user   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique

  proposal proposal[]
  contract contract[]
  member   member[]
  room     room[]

  payment_method payment_method[]
  transaction    transaction[]
}

model session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model verificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

// Manager models
model proposal {
  id String @id @default(cuid())

  name        String
  description String? @db.Text

  status status @default(created)

  links String[]

  milestones   Json[]
  fixed_budget Float

  total_budget Float

  duration                       Int
  agreed_to_terms_and_conditions Boolean

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  account   account @relation(fields: [accountId], references: [id])
  accountId String

  contract contract[]
}

model document {
  id String @id @default(cuid())

  name String
  path String @unique

  file_type String
  size      Int

  status status @default(created)

  category String

  association_entity String
  association_id     String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model contract {
  id String @id @default(cuid())

  status status @default(created)

  agreed_to_terms_and_conditions Boolean @default(false)

  total_value     Float? @default(0)
  paid_value      Float? @default(0)
  remaining_value Float? @default(0)

  start_date DateTime
  end_date   DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  proposal   proposal? @relation(fields: [proposalId], references: [id])
  proposalId String?

  room room[]

  account   account @relation(fields: [accountId], references: [id])
  accountId String
}

model member {
  id String @id @default(cuid())

  account   account @relation(fields: [accountId], references: [id])
  accountId String

  room   room   @relation(fields: [roomId], references: [id])
  roomId String
}

model room {
  id String @id @default(cuid())

  contract   contract? @relation(fields: [contractId], references: [id])
  contractId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  messages message[]
  members  member[]

  account   account @relation(fields: [accountId], references: [id])
  accountId String
}

model message {
  id String @id @default(cuid())

  content String

  associations Json[]

  sent_from String
  type      MessageType @default(text)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  room   room?   @relation(fields: [roomId], references: [id])
  roomId String?
}

model role {
  id String @id @default(cuid())

  name        String? @unique
  description String?

  status status @default(created)

  createdAt DateTime  @default(now())
  updatedAt DateTime?

  permissions Json

  user user[]
}

// Push Notification Models
model pushSubscription {
  id String @id @default(cuid())

  userId    String
  endpoint  String
  p256dhKey String
  authKey   String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
  @@index([userId])
}

model notificationLog {
  id String @id @default(cuid())

  type           String // 'push', 'email', 'sms', etc.
  title          String
  body           String
  payload        String? // JSON payload
  recipientCount Int     @default(0)
  successCount   Int     @default(0)
  failureCount   Int     @default(0)

  sentBy String
  sentAt DateTime @default(now())

  user user @relation(fields: [sentBy], references: [id], onDelete: Cascade)

  @@index([type])
  @@index([sentBy])
  @@index([sentAt])
}

// User notifications - individual notifications for users
model notification {
  id String @id @default(cuid())

  // Basic notification info
  title    String
  message  String
  type     String // 'room', 'chat', 'proposal', 'document', 'contract', 'systemAlerts', 'roleChanges', 'weeklyDigest'
  category String // 'email', 'push', 'inApp'

  // Status
  isRead    Boolean @default(false)
  isDeleted Boolean @default(false)

  // Timestamps
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  readAt    DateTime?
  deletedAt DateTime?

  // User relation
  userId String
  user   user   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Optional metadata
  data Json? // Additional data like entityId, entityType, actionUrl, etc.

  // Priority and expiration
  priority  String    @default("normal") // 'low', 'normal', 'high', 'urgent'
  expiresAt DateTime?

  // Source tracking
  sourceId   String? // ID of the entity that triggered this notification
  sourceType String? // Type of the entity (e.g., 'message', 'document', 'contract')

  // Delivery tracking
  deliveredAt DateTime?
  failedAt    DateTime?
  retryCount  Int       @default(0)

  @@index([userId])
  @@index([userId, isRead])
  @@index([userId, isDeleted])
  @@index([userId, createdAt])
  @@index([type])
  @@index([category])
  @@index([priority])
  @@index([expiresAt])
  @@index([sourceId, sourceType])
}

model payment_method {
  id String @id @default(cuid())

  vault_id String? @default("")

  holder_name  String
  ref_number   String
  expiry_month String
  expiry_year  String
  cvv          String
  type         PaymentMethodType @default(visa)
  isDefault    Boolean           @default(false)

  status status @default(created)

  createdAt DateTime  @default(now())
  updatedAt DateTime?

  account     account       @relation(fields: [accountId], references: [id])
  accountId   String
  transaction transaction[]
}

model transaction {
  id String @id @default(cuid())

  orderId String?

  type   TransactionType @default(full_payment)
  amount Float

  payment_method    payment_method? @relation(fields: [payment_method_id], references: [id])
  payment_method_id String?

  condition   String?
  status      status  @default(created)
  description String?

  createdAt DateTime  @default(now())
  updatedAt DateTime?

  account   account @relation(fields: [accountId], references: [id])
  accountId String
}

model socket {
  id String @id @unique @default(cuid())

  socketId  String
  sessionId String?
  status    status?

  userId String
  user   user   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime  @default(now())
  updatedAt DateTime?

  @@unique([userId, socketId])
}

model nest_tender {
  id String @id @unique @default(cuid())

  ocid String @unique

  language String?

  initiation_type String?
  description     String?
  address         Json?
  timeline        Json?

  procuring_entity String?
  procuring_method String?

  items   Json[]
  parties Json[]

  category String?

  status status @default(created)

  createdAt DateTime  @default(now())
  updatedAt DateTime?
}

model nest_tender_cron_log {
  id String @id @unique @default(cuid())

  last_run DateTime  @default(now())
  next_run DateTime?
}

model subscription {
  id String @id @unique @default(cuid())

  name        String  @unique
  description String?
  status      status  @default(active)

  features Json[]

  price Float

  subscribers Int @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum TransactionType {
  full_payment
  partial_payment
  advanced_payment
}

enum PaymentMethodType {
  visa
  mastercard
  amex
  discover
  paypal
  bank
  apple
  google
}

enum MessageType {
  text
  image
  file
  form
  reply
}

enum status {
  online
  offline

  active
  inactive

  submitted
  received
  negotiating
  agreed

  created
  inprogress
  reviewing
  completed
  closed
  terminated

  pending
}

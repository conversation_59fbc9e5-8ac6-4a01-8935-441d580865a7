/**
 * Test file for BaseService row index methods
 * 
 * These tests verify the getLastModelRowIndex methods work correctly
 * for calculating offsets based on createdAt dates.
 */

import { BaseService } from '../base';
import { prisma } from '@/lib/common/prisma';

// Mock the prisma client
jest.mock('@/lib/common/prisma', () => ({
  prisma: {
    $transaction: jest.fn(),
    $queryRaw: jest.fn(),
  }
}));

// Create a test service that extends BaseService
class TestService extends BaseService {
  constructor() {
    super({
      enableLogging: false, // Disable logging for tests
      validateInput: false,
      validateOutput: false,
    });
  }

  // Expose protected methods for testing
  public async testGetLastModelRowIndex(beforeDate?: Date, createdAtField?: string) {
    return this.getLastModelRowIndex(beforeDate, createdAtField);
  }

  public async testGetLastModelRowIndexByDays(daysSince?: number, createdAtField?: string) {
    return this.getLastModelRowIndexByDays(daysSince, createdAtField);
  }

  public async testCountRecords(options: any) {
    return this.countRecords(options);
  }
}

describe('BaseService Row Index Methods', () => {
  let testService: TestService;
  let mockModel: any;

  beforeEach(() => {
    testService = new TestService();
    
    // Mock model with count method
    mockModel = {
      count: jest.fn()
    };
    
    testService.setModel(mockModel);
    jest.clearAllMocks();
  });

  describe('getLastModelRowIndex', () => {
    it('should count records created before specified date', async () => {
      const testDate = new Date('2024-01-15T00:00:00Z');
      const expectedCount = 42;
      
      mockModel.count.mockResolvedValue(expectedCount);

      const result = await testService.testGetLastModelRowIndex(testDate);

      expect(result).toBe(expectedCount);
      expect(mockModel.count).toHaveBeenCalledWith({
        where: {
          createdAt: {
            lte: testDate
          }
        }
      });
    });

    it('should use current date when no date provided', async () => {
      const expectedCount = 25;
      mockModel.count.mockResolvedValue(expectedCount);

      const result = await testService.testGetLastModelRowIndex();

      expect(result).toBe(expectedCount);
      expect(mockModel.count).toHaveBeenCalledWith({
        where: {
          createdAt: {
            lte: expect.any(Date)
          }
        }
      });
    });

    it('should use custom createdAt field name', async () => {
      const testDate = new Date('2024-01-15T00:00:00Z');
      const customField = 'customCreatedAt';
      const expectedCount = 15;
      
      mockModel.count.mockResolvedValue(expectedCount);

      const result = await testService.testGetLastModelRowIndex(testDate, customField);

      expect(result).toBe(expectedCount);
      expect(mockModel.count).toHaveBeenCalledWith({
        where: {
          [customField]: {
            lte: testDate
          }
        }
      });
    });

    it('should handle errors gracefully', async () => {
      const testError = new Error('Database error');
      mockModel.count.mockRejectedValue(testError);

      await expect(testService.testGetLastModelRowIndex()).rejects.toThrow('Database error');
    });
  });

  describe('getLastModelRowIndexByDays', () => {
    it('should count records within specified day range', async () => {
      const daysSince = 7;
      const expectedCount = 10;
      
      mockModel.count.mockResolvedValue(expectedCount);

      const result = await testService.testGetLastModelRowIndexByDays(daysSince);

      expect(result).toBe(expectedCount);
      expect(mockModel.count).toHaveBeenCalledWith({
        where: {
          createdAt: {
            gte: expect.any(Date),
            lte: expect.any(Date)
          }
        }
      });

      // Verify the date range is approximately correct (within 1 second tolerance)
      const call = mockModel.count.mock.calls[0][0];
      const gteDate = call.where.createdAt.gte;
      const lteDate = call.where.createdAt.lte;
      
      const expectedGte = new Date(Date.now() - daysSince * 24 * 60 * 60 * 1000);
      const expectedLte = new Date();
      
      expect(Math.abs(gteDate.getTime() - expectedGte.getTime())).toBeLessThan(1000);
      expect(Math.abs(lteDate.getTime() - expectedLte.getTime())).toBeLessThan(1000);
    });

    it('should use default 30 days when no days specified', async () => {
      const expectedCount = 100;
      mockModel.count.mockResolvedValue(expectedCount);

      const result = await testService.testGetLastModelRowIndexByDays();

      expect(result).toBe(expectedCount);
      expect(mockModel.count).toHaveBeenCalled();
    });

    it('should use custom createdAt field name', async () => {
      const daysSince = 14;
      const customField = 'customTimestamp';
      const expectedCount = 33;
      
      mockModel.count.mockResolvedValue(expectedCount);

      const result = await testService.testGetLastModelRowIndexByDays(daysSince, customField);

      expect(result).toBe(expectedCount);
      expect(mockModel.count).toHaveBeenCalledWith({
        where: {
          [customField]: {
            gte: expect.any(Date),
            lte: expect.any(Date)
          }
        }
      });
    });

    it('should handle errors gracefully', async () => {
      const testError = new Error('Database connection failed');
      mockModel.count.mockRejectedValue(testError);

      await expect(testService.testGetLastModelRowIndexByDays(7)).rejects.toThrow('Database connection failed');
    });
  });

  describe('Integration scenarios', () => {
    it('should work with different date ranges', async () => {
      // Test different day ranges
      const testCases = [1, 7, 30, 90, 365];
      
      for (const days of testCases) {
        mockModel.count.mockResolvedValue(days * 2); // Mock different counts
        
        const result = await testService.testGetLastModelRowIndexByDays(days);
        expect(result).toBe(days * 2);
      }
      
      expect(mockModel.count).toHaveBeenCalledTimes(testCases.length);
    });

    it('should handle zero and negative day values', async () => {
      mockModel.count.mockResolvedValue(0);
      
      // Zero days should still work (same day)
      const resultZero = await testService.testGetLastModelRowIndexByDays(0);
      expect(resultZero).toBe(0);
      
      // Negative days should work (future date range)
      const resultNegative = await testService.testGetLastModelRowIndexByDays(-7);
      expect(resultNegative).toBe(0);
    });
  });
});
